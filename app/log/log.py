import sys
import time
import logging
import re
from datetime import datetime
from types import FrameType
from typing import cast, Dict
from loguru import logger

from app.core.ctx import CTX_X_REQUEST_ID
from app.settings import APP_SETTINGS

# 配置Tortoise ORM的日志记录器
tortoise_logger = logging.getLogger("tortoise")
# 默认设置为WARNING级别，禁用SQL日志
tortoise_logger.setLevel(logging.WARNING)

# 日志配置控制标志
_sql_logging_enabled = False
_current_log_level = "DEBUG"  # 默认日志级别


def set_sql_logging_enabled(enabled: bool):
    """设置SQL日志是否启用"""
    global _sql_logging_enabled

    # 只在状态真正改变时才打印日志和调整设置
    if _sql_logging_enabled != enabled:
        _sql_logging_enabled = enabled

        # 动态调整Tortoise ORM日志级别
        if enabled:
            tortoise_logger.setLevel(logging.DEBUG)
            logger.info("🗄️ SQL日志已启用")
        else:
            tortoise_logger.setLevel(logging.WARNING)
            logger.info("🗄️ SQL日志已禁用")


def set_log_level(level: str):
    """设置日志级别"""
    global _current_log_level
    _current_log_level = level.upper()
    # 不在这里输出日志，避免在重新配置日志记录器之前产生日志


def get_current_log_level() -> str:
    """获取当前日志级别"""
    return _current_log_level


def is_sql_logging_enabled() -> bool:
    """检查SQL日志是否启用"""
    return _sql_logging_enabled


def x_request_id_filter(record):
    record["x_request_id"] = CTX_X_REQUEST_ID.get()
    return record["x_request_id"]


# 请求计时器，用于存储每个请求的开始时间和处理时间
class RequestTimer:
    _instance = None
    _request_times: Dict[str, float] = {}

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(RequestTimer, cls).__new__(cls)
        return cls._instance

    def start_timer(self, request_id):
        """开始计时"""
        self._request_times[request_id] = time.time()

    def get_elapsed_time(self, request_id):
        """获取处理时间（毫秒）"""
        start_time = self._request_times.get(request_id)
        if start_time:
            elapsed = (time.time() - start_time) * 1000
            # 清理，防止内存泄漏
            del self._request_times[request_id]
            return elapsed
        return None


# 全局请求计时器实例
request_timer = RequestTimer()


class Logger:
    """输出日志到文件和控制台"""

    def __init__(self):
        self.logger = logger
        self.logger.remove()
        APP_SETTINGS.LOGS_ROOT.mkdir(parents=True, exist_ok=True)
        self._setup_loggers()

    def _setup_loggers(self):
        """设置日志记录器"""
        log_name = f"Fast_{time.strftime('%Y-%m-%d', time.localtime()).replace('-', '_')}.log"
        log_path = APP_SETTINGS.LOGS_ROOT / log_name

        # 获取当前日志级别
        current_level = get_current_log_level()

        # 设置控制台输出
        self.logger.add(sys.stdout, level=current_level)

        # 设置文件输出，使用固定的3天保留期
        self.logger.add(
            log_path,
            format="{time:YYYY-MM-DD HH:mm:ss} - "
            "{process.name} | "
            "{thread.name} | "
            "<red> {x_request_id} </red> | "
            "{module}.{function}:{line} - {level} -{message}",
            encoding="utf-8",
            retention="3 days",
            backtrace=True,
            diagnose=True,
            enqueue=True,
            rotation="00:00",
            filter=x_request_id_filter,
            level=current_level,
        )

    def reconfigure_loggers(self):
        """重新配置日志记录器（当设置发生变化时调用）"""
        self.logger.remove()
        self._setup_loggers()
        # 在重新配置后输出确认日志
        current_level = get_current_log_level()
        logger.info(f"🔄 日志配置已重新加载，当前日志级别: {current_level}")

    @staticmethod
    def init_config():
        LOGGER_NAMES = ("uvicorn.asgi", "uvicorn.access", "uvicorn", "tortoise")

        logging.getLogger().handlers = [InterceptHandler()]
        for logger_name in LOGGER_NAMES:
            logging_logger = logging.getLogger(logger_name)
            logging_logger.handlers = [InterceptHandler()]
            # 为Tortoise ORM默认设置WARNING级别，禁用SQL查询日志
            # SQL日志的启用/禁用通过set_sql_logging_enabled函数控制
            if logger_name == "tortoise":
                logging_logger.setLevel(logging.WARNING)

    def get_logger(self):
        return self.logger


# 全局Logger实例
_global_logger_instance = None


def get_global_logger_instance():
    """获取全局Logger实例"""
    global _global_logger_instance
    if _global_logger_instance is None:
        _global_logger_instance = Logger()
    return _global_logger_instance


def reconfigure_global_logger():
    """重新配置全局日志记录器"""
    global _global_logger_instance
    if _global_logger_instance is not None:
        _global_logger_instance.reconfigure_loggers()


class InterceptHandler(logging.Handler):
    def emit(self, record: logging.LogRecord) -> None:
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = str(record.levelno)

        # 检查当前日志级别设置，只处理符合级别要求的日志
        current_level = get_current_log_level()
        level_mapping = {
            "DEBUG": 10,
            "INFO": 20,
            "WARNING": 30,
            "ERROR": 40,
            "CRITICAL": 50
        }

        current_level_num = level_mapping.get(current_level, 20)  # 默认INFO级别
        record_level_num = record.levelno

        # 如果记录的日志级别低于当前设置的级别，则不处理
        if record_level_num < current_level_num:
            return

        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = cast(FrameType, frame.f_back)
            depth += 1

        message = record.getMessage()

        # 处理SQL日志
        if record.name == "tortoise":
            # 检查是否启用SQL日志
            if not _sql_logging_enabled:
                return  # 如果未启用SQL日志，直接返回不处理

            # 为SQL查询添加特殊格式
            if "SELECT" in message or "INSERT" in message or "UPDATE" in message or "DELETE" in message:
                message = f"🗄️ SQL: {message}"
            elif "PRAGMA" in message:
                message = f"🔧 PRAGMA: {message}"
            else:
                message = f"📊 DB: {message}"

        # 处理访问日志
        elif record.name == "uvicorn.access":
            # 匹配访问日志格式: 127.0.0.1:34957 - "GET /api/v1/auth/user-info HTTP/1.1" 200
            access_log_pattern = r'([^:]+):(\d+) - "([A-Z]+) ([^ ]+) HTTP/\d\.\d" (\d+)'
            match = re.match(access_log_pattern, message)

            if match:
                client_ip, client_port, method, path, status = match.groups()

                # 从APILoggerMiddleware中获取处理时间
                elapsed_time = getattr(record, "process_time", None)
                if elapsed_time is None:
                    # 尝试从当前上下文获取request_id
                    request_id = CTX_X_REQUEST_ID.get()
                    if request_id:
                        elapsed_time = request_timer.get_elapsed_time(request_id)

                # 添加处理时间到日志消息
                if elapsed_time is not None:
                    message = f"{message} - {elapsed_time:.2f}ms"

        logger.opt(depth=depth, exception=record.exc_info).log(level, message)


Loggers = Logger()
Loggers.init_config()
log = Loggers.get_logger()
